import { ApiProperty } from '@nestjs/swagger';

/**
 * 统一API响应格式
 */
export class BaseResponseDto<T> {
  @ApiProperty({ description: '是否成功', example: true })
  success: boolean;

  @ApiProperty({ description: '响应消息', example: '操作成功' })
  message: string;

  @ApiProperty({ description: '响应数据', required: false })
  data?: T;

  @ApiProperty({ description: '错误码', required: false, example: 'VALIDATION_ERROR' })
  errorCode?: string;
}
