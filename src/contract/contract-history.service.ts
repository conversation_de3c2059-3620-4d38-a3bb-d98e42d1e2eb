import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { ResultResponse, ResultUtil } from '../common/utils/result.util';

import { ContractHistory } from './entities/contract-history.entity';

export interface CreateContractHistoryDto {
  userInput: string;
  backendLogs: string;
  contractMarkdown: string;
  selectedTemplateName?: string;
  templateMarkdown?: string;
  userId?: number;
  sessionId?: string;
}

@Injectable()
export class ContractHistoryService {
  private readonly logger = new Logger(ContractHistoryService.name);

  constructor(
    @InjectRepository(ContractHistory)
    private readonly contractHistoryRepository: Repository<ContractHistory>,
  ) {}

  /**
   * 保存合同生成历史记录
   * @param createDto 创建历史记录的数据
   * @returns 保存结果
   */
  async saveHistory(createDto: CreateContractHistoryDto): Promise<ResultResponse<ContractHistory>> {
    return ResultUtil.execute(async () => {
      this.logger.log(`保存合同生成历史记录，会话ID: ${createDto.sessionId}`);
      
      const history = this.contractHistoryRepository.create(createDto);
      const savedHistory = await this.contractHistoryRepository.save(history);
      
      this.logger.log(`历史记录保存成功，ID: ${savedHistory.id}`);
      return savedHistory;
    }, '保存历史记录失败');
  }

  /**
   * 根据会话ID查询历史记录
   * @param sessionId 会话ID
   * @returns 历史记录
   */
  async findBySessionId(sessionId: string): Promise<ResultResponse<ContractHistory[]>> {
    return ResultUtil.execute(async () => {
      const histories = await this.contractHistoryRepository.find({
        where: { sessionId },
        order: { createdAt: 'DESC' },
      });
      return histories;
    }, '查询历史记录失败');
  }

  /**
   * 根据用户ID查询历史记录
   * @param userId 用户ID
   * @param limit 限制数量
   * @returns 历史记录列表
   */
  async findByUserId(userId: number, limit: number = 50): Promise<ResultResponse<ContractHistory[]>> {
    return ResultUtil.execute(async () => {
      const histories = await this.contractHistoryRepository.find({
        where: { userId },
        order: { createdAt: 'DESC' },
        take: limit,
      });
      return histories;
    }, '查询用户历史记录失败');
  }

  /**
   * 获取最近的历史记录
   * @param limit 限制数量
   * @returns 历史记录列表
   */
  async findRecent(limit: number = 20): Promise<ResultResponse<ContractHistory[]>> {
    return ResultUtil.execute(async () => {
      const histories = await this.contractHistoryRepository.find({
        order: { createdAt: 'DESC' },
        take: limit,
      });
      return histories;
    }, '查询最近历史记录失败');
  }

  /**
   * 根据ID删除历史记录
   * @param id 历史记录ID
   * @returns 删除结果
   */
  async deleteById(id: number): Promise<ResultResponse<void>> {
    return ResultUtil.execute(async () => {
      const result = await this.contractHistoryRepository.delete(id);
      if (result.affected === 0) {
        return ResultUtil.fail('历史记录不存在', 'HISTORY_NOT_FOUND');
      }
      this.logger.log(`历史记录删除成功，ID: ${id}`);
    }, '删除历史记录失败');
  }
}
