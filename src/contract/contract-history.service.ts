import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Repository } from 'typeorm';

import { ResultResponse, ResultUtil } from '../common/utils/result.util';

import {
  ContractHistoryListItemDto,
  CreateContractHistoryDto,
  HistoryQueryDto,
} from './dto/contract-history.dto';
import { ContractHistory } from './entities/contract-history.entity';

@Injectable()
export class ContractHistoryService {
  private readonly logger = new Logger(ContractHistoryService.name);

  constructor(
    @InjectRepository(ContractHistory)
    private readonly contractHistoryRepository: Repository<ContractHistory>,
  ) {}

  /**
   * 生成历史记录标题（取用户输入前30个字符，去除空白）
   * @param userInput 用户输入
   * @returns 标题
   */
  private generateTitle(userInput: string): string {
    // 去除所有空白字符（空格、换行、制表符等）
    const cleanInput = userInput.replace(/\s+/g, '');
    // 取前30个字符
    return cleanInput.length > 30 ? cleanInput.substring(0, 30) + '...' : cleanInput;
  }

  /**
   * 保存合同生成历史记录
   * @param createDto 创建历史记录的数据
   * @returns 保存结果
   */
  async saveHistory(createDto: CreateContractHistoryDto): Promise<ResultResponse<ContractHistory>> {
    return ResultUtil.execute(async () => {
      this.logger.log(`保存合同生成历史记录，会话ID: ${createDto.sessionId}`);

      // 自动生成标题
      const title = this.generateTitle(createDto.userInput);

      const history = this.contractHistoryRepository.create({
        ...createDto,
        title,
      });
      const savedHistory = await this.contractHistoryRepository.save(history);

      this.logger.log(`历史记录保存成功，ID: ${savedHistory.id}, 标题: ${title}`);
      return savedHistory;
    }, '保存历史记录失败');
  }

  /**
   * 根据会话ID查询历史记录详情（返回完整信息，基于用户权限）
   * @param userId 用户ID
   * @param sessionId 会话ID
   * @returns 历史记录详情
   */
  async findDetailBySessionId(
    userId: number,
    sessionId: string,
  ): Promise<ResultResponse<ContractHistory | null>> {
    return ResultUtil.execute(async () => {
      return await this.contractHistoryRepository.findOne({
        where: { sessionId, userId },
        order: { createdAt: 'DESC' },
      });
    }, '查询历史记录详情失败');
  }

  /**
   * 分页获取历史记录列表（支持过滤，只返回必要字段，基于用户权限）
   * @param userId 用户ID
   * @param queryDto 查询参数（包含分页和过滤条件）
   * @returns 分页历史记录列表
   */
  async findHistoryListWithFilter(
    userId: number,
    queryDto: HistoryQueryDto,
  ): Promise<ResultResponse<PaginatedResponseDto<ContractHistoryListItemDto>>> {
    return ResultUtil.execute(async () => {
      const {
        page = 1,
        pageSize = 20,
        title,
        content,
        templateName,
        startDate,
        endDate,
      } = queryDto;
      const skip = (page - 1) * pageSize;

      // 构建查询条件
      const queryBuilder = this.contractHistoryRepository
        .createQueryBuilder('history')
        .where('history.userId = :userId', { userId });

      // 标题过滤
      if (title) {
        queryBuilder.andWhere('history.title LIKE :title', { title: `%${title}%` });
      }

      // 合同内容过滤
      if (content) {
        queryBuilder.andWhere('history.contractMarkdown LIKE :content', {
          content: `%${content}%`,
        });
      }

      // 模板名称过滤
      if (templateName) {
        queryBuilder.andWhere('history.selectedTemplateName LIKE :templateName', {
          templateName: `%${templateName}%`,
        });
      }

      // 时间范围过滤
      if (startDate) {
        queryBuilder.andWhere('history.createdAt >= :startDate', {
          startDate: new Date(startDate),
        });
      }
      if (endDate) {
        queryBuilder.andWhere('history.createdAt <= :endDate', { endDate: new Date(endDate) });
      }

      // 查询总数
      const total = await queryBuilder.getCount();

      // 查询数据，只选择必要字段
      const histories = await queryBuilder
        .select([
          'history.id',
          'history.title',
          'history.sessionId',
          'history.selectedTemplateName',
          'history.createdAt',
          'history.updatedAt',
        ])
        .orderBy('history.createdAt', 'DESC')
        .skip(skip)
        .take(pageSize)
        .getMany();

      const totalPages = Math.ceil(total / pageSize);

      const result: PaginatedResponseDto<ContractHistoryListItemDto> = {
        items: histories.map((history) => ({
          id: history.id,
          title: history.title,
          sessionId: history.sessionId,
          selectedTemplateName: history.selectedTemplateName,
          createdAt: history.createdAt,
          updatedAt: history.updatedAt,
        })),
        total,
        page,
        pageSize,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };

      this.logger.log(
        `查询历史记录列表，用户ID: ${userId}, 总数: ${total}, 过滤条件: ${JSON.stringify({
          title,
          content,
          templateName,
          startDate,
          endDate,
        })}`,
      );

      return result;
    }, '查询历史记录列表失败');
  }

  /**
   * 分页获取历史记录列表（只返回必要字段，基于用户权限）
   * @param userId 用户ID
   * @param paginationQuery 分页查询参数
   * @returns 分页历史记录列表
   */
  async findHistoryList(
    userId: number,
    paginationQuery: PaginationQueryDto,
  ): Promise<ResultResponse<PaginatedResponseDto<ContractHistoryListItemDto>>> {
    return ResultUtil.execute(async () => {
      const { page = 1, pageSize = 20 } = paginationQuery;
      const skip = (page - 1) * pageSize;

      // 查询总数（基于用户ID）
      const total = await this.contractHistoryRepository.count({
        where: { userId },
      });

      // 查询数据，只选择必要字段（基于用户ID）
      const histories = await this.contractHistoryRepository.find({
        select: ['id', 'title', 'sessionId', 'selectedTemplateName', 'createdAt', 'updatedAt'],
        where: { userId },
        order: { createdAt: 'DESC' },
        skip,
        take: pageSize,
      });

      const totalPages = Math.ceil(total / pageSize);

      const result: PaginatedResponseDto<ContractHistoryListItemDto> = {
        items: histories.map((history) => ({
          id: history.id,
          title: history.title,
          sessionId: history.sessionId,
          selectedTemplateName: history.selectedTemplateName,
          createdAt: history.createdAt,
          updatedAt: history.updatedAt,
        })),
        total,
        page,
        pageSize,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };

      return result;
    }, '查询历史记录列表失败');
  }

  /**
   * 根据会话ID删除历史记录（基于用户权限）
   * @param userId 用户ID
   * @param sessionId 会话ID
   * @returns 删除结果
   */
  async deleteBySessionId(userId: number, sessionId: string): Promise<ResultResponse<void>> {
    return ResultUtil.execute(async () => {
      // 先查找记录是否存在且属于该用户
      const history = await this.contractHistoryRepository.findOne({
        where: { sessionId, userId },
      });

      if (!history) {
        return ResultUtil.fail('历史记录不存在或无权限删除', 'HISTORY_NOT_FOUND_OR_NO_PERMISSION');
      }

      // 删除记录
      const result = await this.contractHistoryRepository.delete(history.id);
      if (result.affected === 0) {
        return ResultUtil.fail('删除失败', 'DELETE_FAILED');
      }

      this.logger.log(`历史记录删除成功，会话ID: ${sessionId}, 用户ID: ${userId}`);
    }, '删除历史记录失败');
  }

  /**
   * 根据会话ID更新历史记录标题（基于用户权限）
   * @param userId 用户ID
   * @param sessionId 会话ID
   * @param newTitle 新标题
   * @returns 更新结果
   */
  async updateTitleBySessionId(
    userId: number,
    sessionId: string,
    newTitle: string,
  ): Promise<ResultResponse<ContractHistory>> {
    return ResultUtil.execute(async () => {
      // 先查找记录是否存在且属于该用户
      const history = await this.contractHistoryRepository.findOne({
        where: { sessionId, userId },
      });

      if (!history) {
        return ResultUtil.fail('历史记录不存在或无权限修改', 'HISTORY_NOT_FOUND_OR_NO_PERMISSION');
      }

      // 更新标题
      history.title = newTitle.length > 100 ? newTitle.substring(0, 100) : newTitle;
      const updatedHistory = await this.contractHistoryRepository.save(history);

      this.logger.log(
        `历史记录标题更新成功，会话ID: ${sessionId}, 用户ID: ${userId}, 新标题: ${newTitle}`,
      );
      return updatedHistory;
    }, '更新历史记录标题失败');
  }
}
