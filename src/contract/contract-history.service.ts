import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { ResultResponse, ResultUtil } from '../common/utils/result.util';

import {
  ContractHistoryListItemDto,
  CreateContractHistoryDto,
  PaginatedResponseDto,
  PaginationQueryDto,
} from './dto/contract-history.dto';
import { ContractHistory } from './entities/contract-history.entity';

@Injectable()
export class ContractHistoryService {
  private readonly logger = new Logger(ContractHistoryService.name);

  constructor(
    @InjectRepository(ContractHistory)
    private readonly contractHistoryRepository: Repository<ContractHistory>,
  ) {}

  /**
   * 生成历史记录标题（取用户输入前30个字符，去除空白）
   * @param userInput 用户输入
   * @returns 标题
   */
  private generateTitle(userInput: string): string {
    // 去除所有空白字符（空格、换行、制表符等）
    const cleanInput = userInput.replace(/\s+/g, '');
    // 取前30个字符
    return cleanInput.length > 30 ? cleanInput.substring(0, 30) + '...' : cleanInput;
  }

  /**
   * 保存合同生成历史记录
   * @param createDto 创建历史记录的数据
   * @returns 保存结果
   */
  async saveHistory(createDto: CreateContractHistoryDto): Promise<ResultResponse<ContractHistory>> {
    return ResultUtil.execute(async () => {
      this.logger.log(`保存合同生成历史记录，会话ID: ${createDto.sessionId}`);

      // 自动生成标题
      const title = this.generateTitle(createDto.userInput);

      const history = this.contractHistoryRepository.create({
        ...createDto,
        title,
      });
      const savedHistory = await this.contractHistoryRepository.save(history);

      this.logger.log(`历史记录保存成功，ID: ${savedHistory.id}, 标题: ${title}`);
      return savedHistory;
    }, '保存历史记录失败');
  }

  /**
   * 根据会话ID查询历史记录详情（返回完整信息）
   * @param sessionId 会话ID
   * @returns 历史记录详情
   */
  async findDetailBySessionId(sessionId: string): Promise<ResultResponse<ContractHistory | null>> {
    return ResultUtil.execute(async () => {
      const history = await this.contractHistoryRepository.findOne({
        where: { sessionId },
        order: { createdAt: 'DESC' },
      });
      return history;
    }, '查询历史记录详情失败');
  }

  /**
   * 根据会话ID查询历史记录（保留原方法，用于兼容）
   * @param sessionId 会话ID
   * @returns 历史记录
   */
  async findBySessionId(sessionId: string): Promise<ResultResponse<ContractHistory[]>> {
    return ResultUtil.execute(async () => {
      const histories = await this.contractHistoryRepository.find({
        where: { sessionId },
        order: { createdAt: 'DESC' },
      });
      return histories;
    }, '查询历史记录失败');
  }

  /**
   * 根据用户ID查询历史记录
   * @param userId 用户ID
   * @param limit 限制数量
   * @returns 历史记录列表
   */
  async findByUserId(userId: number, limit: number = 50): Promise<ResultResponse<ContractHistory[]>> {
    return ResultUtil.execute(async () => {
      const histories = await this.contractHistoryRepository.find({
        where: { userId },
        order: { createdAt: 'DESC' },
        take: limit,
      });
      return histories;
    }, '查询用户历史记录失败');
  }

  /**
   * 分页获取历史记录列表（只返回必要字段）
   * @param paginationQuery 分页查询参数
   * @returns 分页历史记录列表
   */
  async findHistoryList(
    paginationQuery: PaginationQueryDto,
  ): Promise<ResultResponse<PaginatedResponseDto<ContractHistoryListItemDto>>> {
    return ResultUtil.execute(async () => {
      const { page = 1, pageSize = 20 } = paginationQuery;
      const skip = (page - 1) * pageSize;

      // 查询总数
      const total = await this.contractHistoryRepository.count();

      // 查询数据，只选择必要字段
      const histories = await this.contractHistoryRepository.find({
        select: ['id', 'title', 'sessionId', 'selectedTemplateName', 'createdAt', 'updatedAt'],
        order: { createdAt: 'DESC' },
        skip,
        take: pageSize,
      });

      const totalPages = Math.ceil(total / pageSize);

      const result: PaginatedResponseDto<ContractHistoryListItemDto> = {
        items: histories.map((history) => ({
          id: history.id,
          title: history.title,
          sessionId: history.sessionId,
          selectedTemplateName: history.selectedTemplateName,
          createdAt: history.createdAt,
          updatedAt: history.updatedAt,
        })),
        total,
        page,
        pageSize,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };

      return result;
    }, '查询历史记录列表失败');
  }

  /**
   * 获取最近的历史记录（保留原方法，用于兼容）
   * @param limit 限制数量
   * @returns 历史记录列表
   */
  async findRecent(limit: number = 20): Promise<ResultResponse<ContractHistory[]>> {
    return ResultUtil.execute(async () => {
      const histories = await this.contractHistoryRepository.find({
        order: { createdAt: 'DESC' },
        take: limit,
      });
      return histories;
    }, '查询最近历史记录失败');
  }

  /**
   * 根据ID删除历史记录
   * @param id 历史记录ID
   * @returns 删除结果
   */
  async deleteById(id: number): Promise<ResultResponse<void>> {
    return ResultUtil.execute(async () => {
      const result = await this.contractHistoryRepository.delete(id);
      if (result.affected === 0) {
        return ResultUtil.fail('历史记录不存在', 'HISTORY_NOT_FOUND');
      }
      this.logger.log(`历史记录删除成功，ID: ${id}`);
    }, '删除历史记录失败');
  }
}
