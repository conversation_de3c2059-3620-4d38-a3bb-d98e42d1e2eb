import { Body, Controller, Get, Post, Query, Sse, UseGuards } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';

import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ResultResponse } from '../common/utils/result.util';

import { ContractHistoryService } from './contract-history.service';
import { ContractService } from './contract.service';
import { ContractGenerateDto } from './dto';

@ApiTags('合同生成')
@Controller('contract')
@UseGuards(JwtAuthGuard)
export class ContractController {
  constructor(
    private readonly contractService: ContractService,
    private readonly contractHistoryService: ContractHistoryService,
  ) {}

  /**
   * 建立SSE连接，传递sessionId
   * @param sessionId 会话ID
   * @returns SSE流
   */
  @Sse('stream')
  @ApiOperation({ summary: '建立SSE连接，等待任务提交' })
  @ApiQuery({
    name: 'sessionId',
    required: true,
    description: '会话ID,用于SSE通知',
  })
  contractStream(@Query('sessionId') sessionId: string) {
    return this.contractService.createSseSession(sessionId);
  }

  /**
   * 提交合同生成任务
   * @param body 合同生成请求数据（包含sessionId和userInput）
   * @returns 提交结果
   */
  @Post('generate_contract')
  @ApiOperation({ summary: '提交合同生成任务' })
  @ApiBody({ type: ContractGenerateDto })
  async submitGenerateTask(@Body() body: ContractGenerateDto): Promise<ResultResponse> {
    return this.contractService.submitTaskToSession(body.sessionId, body.userInput);
  }

  /**
   * 获取最近的历史记录
   * @param limit 限制数量，默认20
   * @returns 历史记录列表
   */
  @Get('history')
  @ApiOperation({ summary: '获取合同生成历史记录' })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: '限制数量，默认20',
    type: Number,
  })
  async getHistory(@Query('limit') limit?: string) {
    const limitNum = limit ? parseInt(limit, 10) : 20;
    return this.contractHistoryService.findRecent(limitNum);
  }

  /**
   * 根据会话ID获取历史记录
   * @param sessionId 会话ID
   * @returns 历史记录列表
   */
  @Get('history/session')
  @ApiOperation({ summary: '根据会话ID获取历史记录' })
  @ApiQuery({
    name: 'sessionId',
    required: true,
    description: '会话ID',
  })
  async getHistoryBySession(@Query('sessionId') sessionId: string) {
    return this.contractHistoryService.findBySessionId(sessionId);
  }
}
