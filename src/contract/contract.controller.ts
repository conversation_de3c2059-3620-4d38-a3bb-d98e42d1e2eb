import { Body, Controller, Post, Query, Sse, UseGuards } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';

import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ResultResponse } from '../common/utils/result.util';

import { ContractService } from './contract.service';
import { ContractGenerateDto } from './dto';

@ApiTags('合同生成')
@Controller('contract')
@UseGuards(JwtAuthGuard)
export class ContractController {
  constructor(private readonly contractService: ContractService) {}

  /**
   * 建立SSE连接，传递sessionId
   * @param sessionId 会话ID
   * @returns SSE流
   */
  @Sse('stream')
  @ApiOperation({ summary: '建立SSE连接，等待任务提交' })
  @ApiQuery({
    name: 'sessionId',
    required: true,
    description: '会话ID,用于SSE通知',
  })
  contractStream(@Query('sessionId') sessionId: string) {
    return this.contractService.createSseSession(sessionId);
  }

  /**
   * 提交合同生成任务
   * @param body 合同生成请求数据（包含sessionId和userInput）
   * @returns 提交结果
   */
  @Post('generate_contract')
  @ApiOperation({ summary: '提交合同生成任务' })
  @ApiBody({ type: ContractGenerateDto })
  async submitGenerateTask(@Body() body: ContractGenerateDto): Promise<ResultResponse> {
    return this.contractService.submitTaskToSession(body.sessionId, body.userInput);
  }
}
