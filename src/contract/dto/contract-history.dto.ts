import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsPositive, Max, Min, IsDateString } from 'class-validator';

/**
 * 创建历史记录DTO
 */
export interface CreateContractHistoryDto {
  userInput: string;
  backendLogs: string;
  contractMarkdown: string;
  selectedTemplateName?: string;
  templateMarkdown?: string;
  userId?: number;
  sessionId?: string;
}

/**
 * 历史记录列表项DTO（用于列表显示，只包含必要字段）
 */
export class ContractHistoryListItemDto {
  @ApiProperty({ description: '历史记录ID' })
  id: number;

  @ApiProperty({ description: '历史记录标题' })
  title: string;

  @ApiProperty({ description: '会话ID' })
  sessionId: string;

  @ApiProperty({ description: '选择的模板名称' })
  selectedTemplateName: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

/**
 * 历史记录查询DTO（包含分页和过滤参数）
 */
export class HistoryQueryDto {
  @ApiProperty({
    description: '页码，从1开始',
    example: 1,
    required: false,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsPositive()
  page?: number = 1;

  @ApiProperty({
    description: '每页数量，最大100',
    example: 20,
    required: false,
    default: 20,
  })
  @IsOptional()
  @Type(() => Number)
  @IsPositive()
  @Max(100)
  @Min(1)
  pageSize?: number = 20;

  @ApiProperty({
    description: '标题关键词搜索（模糊匹配）',
    example: '数据保密',
    required: false,
  })
  @IsOptional()
  title?: string;

  @ApiProperty({
    description: '合同内容关键词搜索（模糊匹配）',
    example: '甲方',
    required: false,
  })
  @IsOptional()
  content?: string;

  @ApiProperty({
    description: '模板名称关键词搜索（模糊匹配）',
    example: '技术服务',
    required: false,
  })
  @IsOptional()
  templateName?: string;

  @ApiProperty({
    description: '开始时间（ISO 8601格式）',
    example: '2024-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    description: '结束时间（ISO 8601格式）',
    example: '2024-12-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;
}

/**
 * 分页查询DTO（保留原有接口，用于兼容）
 */
export class PaginationQueryDto {
  @ApiProperty({
    description: '页码，从1开始',
    example: 1,
    required: false,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsPositive()
  page?: number = 1;

  @ApiProperty({
    description: '每页数量，最大100',
    example: 20,
    required: false,
    default: 20,
  })
  @IsOptional()
  @Type(() => Number)
  @IsPositive()
  @Max(100)
  @Min(1)
  pageSize?: number = 20;
}

/**
 * 分页响应DTO
 */
export class PaginatedResponseDto<T> {
  @ApiProperty({ description: '数据列表' })
  items: T[];

  @ApiProperty({ description: '总数量' })
  total: number;

  @ApiProperty({ description: '当前页码' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  pageSize: number;

  @ApiProperty({ description: '总页数' })
  totalPages: number;

  @ApiProperty({ description: '是否有下一页' })
  hasNext: boolean;

  @ApiProperty({ description: '是否有上一页' })
  hasPrev: boolean;
}

/**
 * 更新历史记录标题DTO
 */
export class UpdateHistoryTitleDto {
  @ApiProperty({
    description: '会话ID',
    example: 'session-123',
  })
  sessionId: string;

  @ApiProperty({
    description: '新的标题',
    example: '软件开发合同-北京科技公司',
    maxLength: 100,
  })
  title: string;
}
