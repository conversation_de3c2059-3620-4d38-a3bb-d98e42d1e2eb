import { ApiProperty } from '@nestjs/swagger';
import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class ContractHistory {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: '历史记录ID' })
  id: number;

  @Column({ length: 100 })
  @ApiProperty({ description: '历史记录标题（用户输入前30个字符）' })
  title: string;

  @Column({ type: 'text' })
  @ApiProperty({ description: '用户输入内容' })
  userInput: string;

  @Column({ type: 'text' })
  @ApiProperty({ description: '后端处理日志' })
  backendLogs: string;

  @Column({ type: 'text' })
  @ApiProperty({ description: '生成的合同Markdown内容' })
  contractMarkdown: string;

  @Column({ nullable: true })
  @ApiProperty({ description: '选择的模板名称' })
  selectedTemplateName: string;

  @Column({ nullable: true })
  @ApiProperty({ description: '模板Markdown内容' })
  templateMarkdown: string;

  @Column({ nullable: true })
  @ApiProperty({ description: '用户ID（如果有用户系统）' })
  userId: number;

  @Column({ nullable: true })
  @ApiProperty({ description: '会话ID' })
  sessionId: string;

  @CreateDateColumn()
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}
