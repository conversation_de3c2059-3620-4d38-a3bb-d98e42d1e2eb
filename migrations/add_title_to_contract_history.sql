-- 为现有的contract_history表添加title列
-- 如果表已存在但没有title列，则添加title列

-- 检查并添加title列
ALTER TABLE contract_history ADD COLUMN title VARCHAR(100);

-- 为现有记录生成title（取userInput前30个字符，去除空白）
UPDATE contract_history 
SET title = CASE 
    WHEN LENGTH(REPLACE(REPLACE(REPLACE(userInput, ' ', ''), CHAR(10), ''), CHAR(13), '')) > 30 
    THEN SUBSTR(REPLACE(REPLACE(REPLACE(userInput, ' ', ''), CHAR(10), ''), CHAR(13), ''), 1, 30) || '...'
    ELSE REPLACE(REPLACE(REPLACE(userInput, ' ', ''), CHAR(10), ''), CHAR(13), '')
END
WHERE title IS NULL;
